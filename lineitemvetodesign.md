## Option 1: Use Requisitionitem

### How It Works
This approach extends the existing RequisitionItems table with dedicated columns to track item removal. When an approver removes a line item, the system sets a flag (IsRemoved = true) and captures removal details directly on the item record. The item remains in the database but is filtered out of purchasing workflows and visually distinguished in the UI. This creates a simple, direct relationship between the item and its removal status.

### Database Changes
Add columns to dbo.RequisitionItems table:
1. IsRemoved (bit) - Flag indicating if item was removed by approver
2. RemovalReasonCode (varchar(50)) - Code representing reason for removal
3. RemovalComment (varchar(500)) - Additional comments from approver
4. RemovedBy (varchar(100)) - Username of approver who removed item
5. RemovalDate (datetime) - Timestamp when item was removed

### RequisitionServices Changes
1. Extend RequisitionItem model with new properties:
    * IsRemoved
    * RemovalReasonCode
    * RemovalComment
    * RemovedBy
    * RemovalDate
2. Update repository methods to filter removed items when appropriate
3. Modify requisition processing to handle items marked as removed

### ProcurementServices Changes 
1. API Endpoints
    * RemoveRequisitionItem(int requisitionId, int requisitionItemId, string reasonCode, string comments)
        * Validates approver permissions
        * Implements removal logic based on chosen option
        * Returns success/failure response
2. Notification Service
    * SendItemRemovedNotification(int requisitionId, List<RemovedItemInfo> removedItems)
        * Sends email to requisitioner with details of removed items
        * Includes reason codes and comments for each item

### Business Logic
* When an approver removes an item, set IsRemoved = true and populate removal fields
* Items with IsRemoved = true should be excluded from purchasing processes
* Removed items remain visible in the UI but are visually distinguished (shaded)
* The requisition total should exclude removed items
* Requisitioner receives notification about removed items

---
## Option 2: Use RequisitionItemStatusHistories Table
### Database Changes
1. Add new status type to dbo.RequisitionItemStatusTypes:
    * "Removed by Approver" status (ID: [next available])
No changes needed to existing table structure


### RequisitionServices Changes
1. Add new entry in RequisitionItemStatusType enum:
    * [Description("Removed by Approver")]
    * RemovedByApprover = [next available ID]
2. Use existing RequisitionItemStatusHistories table to track removal:
    * Set item status to "Removed by Approver"
    * Store reason code and comments in the Comments field
    * CreatedBy already tracks who made the change
    * CreateDate already tracks when change occurred

### ProcurementServices Changes 
1. API Endpoints
    * RemoveRequisitionItem(int requisitionId, int requisitionItemId, string reasonCode, string comments)
        * Validates approver permissions
        * Implements removal logic based on chosen option
        * Returns success/failure response
2. Notification Service
    * SendItemRemovedNotification(int requisitionId, List<RemovedItemInfo> removedItems)
        * Sends email to requisitioner with details of removed items
        * Includes reason codes and comments for each item
  
### Business Logic
* When an approver removes an item, create a new status history record with "Removed by Approver" status
* Store removal reason in format: "Reason: [ReasonCode] - [Comments]"
* Items with "Removed by Approver" status are excluded from purchasing processes
* The UI displays these items as removed (shaded)
* Requisition total excludes items with "Removed by Approver" status
* UNDO functionality simply adds a new status history record with previous status

---
## User Flow regardless of implementation
1. Approver clicks "Remove" on a line item
2. Confirmation modal appears
3. Approver selects reason code and adds optional comments
4. On confirmation, item is marked as removed
5. UI updates to show item as removed
6. Approver can UNDO removal if needed
7. On save, email notification is sent to requisitioner