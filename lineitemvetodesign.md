
This document outlines two options for the technical implementation of a line-item veto for an approver. 

User Flow regardless of implementation:
1. Approver clicks "Remove" on a line item
2. Confirmation modal appears
3. Approver selects reason code from dropdown and adds optional comments
4. On confirmation, item is marked as removed in the database
5. UI updates to show item as removed (shaded yellow with an undo button displayed)
6. Approver can UNDO removal if needed, and the yellow tint and undo button are removed
7. On save, email notification is sent to requisitioner
---
## Option 1: Use Requisitionitem

### How It Works
When an approver removes a line item, the system sets a flag (IsRemoved = true) and captures removal details directly on the item record. The item remains in the database but is visually distinguished in the UI. 

### Database Changes
Add columns to dbo.RequisitionItems table:
1. IsRemoved (bit) - Flag indicating if item was removed by approver
2. RemovalReasonCode (varchar(50)) - Code representing reason for removal
3. RemovalComment (varchar(500)) - Additional comments from approver
4. RemovedBy (varchar(100)) - Username of approver who removed item
5. RemovalDate (datetime) - Timestamp when item was removed

### RequisitionServices Changes
1. Extend RequisitionItem model with new properties:
    * IsRemoved
    * RemovalReasonCode
    * RemovalComment
    * RemovedBy
    * RemovalDate
2. Update repository methods to filter removed items when appropriate
3. Modify requisition processing to handle items marked as removed

### ProcurementServices Changes (External API)
1. **New Endpoint** - `eProcurementServices\Controllers\RequisitionController.cs`
    * RemoveRequisitionItem(requisitionId, requisitionItemId, reasonCode, comments)
        * Validates approver permissions
        * Calls RequisitionServices to implement removal logic
        * Returns success/failure response5
2. **Email Notification Method** - `eProcurementServices.Domain\Service\RequisitionService.cs`
    * SendItemRemovedNotification(requisitionId, List of removedItems)
        * Uses existing email infrastructure with HTML templates and token replacement
        * Creates EmailRequest with LineItemsRemoved type and sends to requisitioner
        * Email includes requisition details, approver name, formatted list of removed items with reasons, and direct link to requisition
        * Leverages existing SMTP delivery system with company branding

### Web Application Changes
1. **Approval Details Controller**
    * Add `removeRequisitionItem` function
    * Handle confirmation modal and API call
    * Update UI after successful removal
2. **ApprovalReqReview Controller**
    * Add same `removeRequisitionItem` function for approval review modal
3. **RequisitionItemList.html** 
    * Add "Remove" button/link for approvers (similar to existing remove functionality on line 26)
    * Show removed items with visual distinction (shaded/grayed out)
    * Display removal reason and comments for removed items
4. **ConfirmationModal.html**
    * Create new removal confirmation modal with reason code dropdown and comments field
    * Include "Undo" functionality
5. **CSS** - Add styles for removed items visual distinction

### Business Logic
* When an approver removes an item, set IsRemoved = true and populate removal fields
* Items with IsRemoved = true should be excluded from purchasing processes
* Removed items remain visible in the UI but are visually distinguished (shaded)
* The requisition total should exclude removed items
* Requisitioner receives notification about removed items

---
## Option 2: Use RequisitionItemStatusHistories Table

### How It Works
This approach leverages the existing status tracking infrastructure by treating item removal as a status change. When an approver removes a line item, the system creates a new status history record with a "Removed by Approver" status. This maintains a complete audit trail of all item state changes and allows for easy undo functionality by simply adding another status record. The current status determines whether an item participates in purchasing workflows, and the UI can display items based on their latest status.

### Database Changes
1. Add new status type to dbo.RequisitionItemStatusTypes:
    * "Removed by Approver" status (ID: [next available])
No changes needed to existing table structure


### RequisitionServices Changes
1. Add new entry in RequisitionItemStatusType enum:
    * [Description("Removed by Approver")]
    * RemovedByApprover = [next available ID]
2. Use existing RequisitionItemStatusHistories table to track removal:
    * Set item status to "Removed by Approver"
    * Store reason code and comments in the Comments field
    * CreatedBy already tracks who made the change
    * CreateDate already tracks when change occurred

### ProcurementServices Changes (External API)
1. **RequisitionController.cs**
    * `RemoveRequisitionItem(int requisitionId, int requisitionItemId, string reasonCode, string comments)`
        * Validates approver permissions
        * Calls RequisitionServices to implement removal logic
2. **RequisitionService.cs**
    * `SendItemRemovedNotification(int requisitionId, List<RemovedItemInfo> removedItems)`
        * Uses existing email infrastructure with HTML templates and token replacement
        * Creates EmailRequest with LineItemsRemoved type and sends to requisitioner
        * Email includes requisition details, approver name, formatted list of removed items with reasons, and direct link to requisition
        * Leverages existing SMTP delivery system with company branding

### Web Application Changes
1. **Approval Details Controller**
    * Add `removeRequisitionItem` function
    * Handle confirmation modal and API call
    * Update UI after successful removal
2. **ApprovalReqReview Controller**
    * Add same `removeRequisitionItem` function for approval review modal
3. **RequisitionItemList.html** 
    * Add "Remove" button/link for approvers (similar to existing remove functionality on line 26)
    * Show removed items with visual distinction (shaded/grayed out)
    * Display removal reason and comments for removed items
4. **ConfirmationModal.html**
    * Create new removal confirmation modal with reason code dropdown and comments field
    * Include "Undo" functionality
5. **CSS** - Add styles for removed items visual distinction* 

### Business Logic
* When an approver removes an item, create a new status history record with "Removed by Approver" status
* Store removal reason in format: "Reason: [ReasonCode] - [Comments]"
* Items with "Removed by Approver" status are excluded from purchasing processes
* The UI displays these items as removed (shaded)
* Requisition total excludes items with "Removed by Approver" status
* UNDO functionality simply adds a new status history record with previous status
